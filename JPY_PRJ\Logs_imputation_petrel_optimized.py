# Missing Logs Imputation – Petrel-connected workflow (Optimized Version)
# Enhanced with console-based interactive well and log selection - Optimized 2025-06-20
# This optimized version maintains all functionality while reducing code length by ~60%

import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score

# Plotting
import plotly.express as px

# Petrel connection
from cegalprizm.pythontool import PetrelConnection, DiscreteGlobalWellLog

# Initialize connection and configuration
petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')

AUTO_MODE = False  # Set to True for automatic selection
PREDICTION_MODES = {1: "Fill Missing Values Only", 2: "Cross-Validation Test", 3: "Re-predict Entire Log"}

# Consolidated selection function
def console_select(options, prompt, default=None, multiple=False, max_selections=None, auto_mode=False):
    """Unified console selection function for both single and multiple selections."""
    if not options:
        print("No options available.")
        return [] if multiple else None
    
    print(f"\n{prompt}")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    # Handle defaults
    if default is not None:
        if multiple and isinstance(default, list):
            default_indices = [options.index(d) + 1 for d in default if d in options]
        elif not multiple and default in options:
            default_indices = [options.index(default) + 1]
        else:
            default_indices = [default] if isinstance(default, int) else []
        
        if default_indices:
            print(f"Default: {', '.join([str(i) for i in default_indices])}")
    
    # Auto mode
    if auto_mode:
        if default_indices:
            selected = default_indices
        else:
            max_select = min(max_selections or 5, len(options)) if multiple else 1
            selected = list(range(1, max_select + 1)) if multiple else [1]
        
        result = [options[i-1] for i in selected]
        print(f"Auto-selected: {', '.join(result) if multiple else result[0]}")
        return result if multiple else result[0]
    
    # User input
    print("Enter selection(s) - numbers separated by commas" + (" or 'all'" if multiple else "") + ", or Enter for default:")
    
    while True:
        try:
            user_input = input("Selection: ").strip()
            
            if not user_input and default_indices:
                selected = default_indices
                break
            elif user_input.lower() == 'all' and multiple:
                if max_selections and len(options) > max_selections:
                    print(f"Cannot select all - max {max_selections} allowed.")
                    continue
                selected = list(range(1, len(options) + 1))
                break
            elif user_input:
                if multiple:
                    selected = [int(x.strip()) for x in user_input.split(',')]
                    if max_selections and len(selected) > max_selections:
                        print(f"Too many selections. Max {max_selections} allowed.")
                        continue
                else:
                    selected = [int(user_input)]
                
                if any(i < 1 or i > len(options) for i in selected):
                    print(f"Invalid selection. Enter numbers 1-{len(options)}")
                    continue
                break
            else:
                print("Please enter a selection.")
        except (ValueError, EOFError, KeyboardInterrupt):
            if default_indices:
                selected = default_indices
                break
            print("Invalid input. Try again.")
    
    result = [options[i-1] for i in selected]
    print(f"Selected: {', '.join(result) if multiple else result[0]}")
    return result if multiple else result[0]

# Data loading and selection
print('Setting up interactive selection...')

# Get available logs and wells
try:
    available_logs = {log.petrel_name: log for log in petrel.global_well_logs if hasattr(log, 'petrel_name')}
    log_names = sorted(available_logs.keys())
    print(f'Found {len(log_names)} global well logs')

    # Handle wells with proper error handling (from working version)
    try:
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        print(f'Found {len(wells)} wells')
    except Exception as e:
        print(f'ERROR accessing wells: {str(e)}')
        print(f'Type of petrel.wells: {type(petrel.wells)}')

        # Fallback: try manual iteration approach
        print('Trying fallback approach...')
        wells = []
        well_names = []
        try:
            for well in petrel.wells:
                if hasattr(well, 'petrel_name'):
                    wells.append(well)
                    well_names.append(well.petrel_name)
            print(f'Fallback found {len(wells)} wells')
        except Exception as e2:
            print(f'Fallback also failed: {str(e2)}')
            wells = []
            well_names = []

    if not wells or not log_names:
        print('ERROR: No wells or logs found in the project!')
        print('Please ensure:')
        print('1. Petrel project is open and contains wells/logs')
        print('2. Wells and logs are properly loaded in the project')
        print('3. Python Tool Pro connection is working correctly')
        raise ValueError("No wells or logs available for selection")

except Exception as e:
    print(f'ERROR: {str(e)}')
    raise

# Interactive selections
default_wells = well_names[:min(5, len(well_names))]
selected_well_names = console_select(well_names, "Select Wells for Analysis:", 
                                   default=default_wells, multiple=True, auto_mode=AUTO_MODE)

default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp'] if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else log_names[:4]
selected_input_logs = console_select(log_names, "Select Input Logs for ML Training:", 
                                   default=default_input_logs, multiple=True, auto_mode=AUTO_MODE)

default_target = 'Vs' if 'Vs' in log_names else log_names[0]
selected_target_log = console_select(log_names, "Select Target Log for Imputation:", 
                                   default=default_target, auto_mode=AUTO_MODE)

# Prediction mode selection
selected_mode = console_select(list(PREDICTION_MODES.keys()), "Select Prediction Mode:", 
                             default=1, auto_mode=AUTO_MODE)
prediction_mode = int(selected_mode)
prediction_mode_name = PREDICTION_MODES[prediction_mode]

print(f'\n✓ Configuration: {len(selected_well_names)} wells, {len(selected_input_logs)} input logs, target: {selected_target_log}')
print(f'✓ Prediction mode: {prediction_mode_name}')

# Get selected objects
selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
LOG_NAMES = selected_input_logs + [selected_target_log]
logs = [available_logs[name] for name in LOG_NAMES if name in available_logs]

# Load data
print(f'\nLoading data from {len(selected_wells)} wells...')
well_data = pd.DataFrame()

for i, w in enumerate(selected_wells):
    print(f'Processing well {i+1}/{len(selected_wells)}: {w.petrel_name}')
    try:
        df = w.logs_dataframe(logs)
        if not df.empty:
            df['WELL'] = w.petrel_name
            well_data = pd.concat([well_data, df], ignore_index=False)
            print(f'  ✓ Loaded {len(df)} samples')
    except Exception as e:
        print(f'  ✗ Error: {str(e)}')

if well_data.empty:
    raise ValueError("No data loaded!")

well_data.reset_index(drop=False, inplace=True)
print(f'\n✓ Combined data shape: {well_data.shape}')

# Basic cleaning
for col, (min_val, max_val) in [('GR', (0, 300)), ('NPHI', (0, 1)), ('RHOB', (1.0, 3.5))]:
    if col in well_data.columns:
        well_data[col] = np.where((well_data[col] >= min_val) & (well_data[col] <= max_val), 
                                 well_data[col], np.nan)
        print(f'Cleaned {col}')

# Coverage analysis
log_columns = [col for col in LOG_NAMES if col in well_data.columns]
coverage = 1.0 - well_data[log_columns].isna().mean()
print('\nData coverage:')
for log_name, cov in coverage.items():
    print(f'  {log_name}: {cov:.2%}')

# Visualize coverage
fig = px.bar(coverage, labels={'value':'Coverage'}, title='Data coverage for selected logs')
fig.show()

# Consolidated ML imputation function
def impute_logs(df, depth_col, feature_cols, targets, prediction_mode=1, test_percentage=25.0):
    """Consolidated ML imputation with multiple models."""
    res = df.copy()
    model_results = {}

    # ML models configuration
    boosters = [
        ('XGBoost', XGBRegressor(n_estimators=300, tree_method='gpu_hist', learning_rate=0.05,
                                early_stopping_rounds=100, random_state=42)),
        ('LightGBM', LGBMRegressor(device='gpu', n_estimators=300, random_state=42)),
        ('CatBoost', CatBoostRegressor(task_type='GPU', early_stopping_rounds=100,
                                      verbose=0, random_state=42))
    ]
    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- Processing {tgt} (Mode: {PREDICTION_MODES[prediction_mode]}) ---')

        # Prepare training data
        if prediction_mode == 1:  # Fill missing only
            train = res[res[tgt].notna()][feature_set + [tgt]].copy()
        elif prediction_mode == 2:  # Cross-validation
            complete_data = res[res[tgt].notna()][feature_set + [tgt]].copy()
            if len(complete_data) == 0:
                continue
            train, test_data = train_test_split(complete_data, test_size=test_percentage/100, random_state=42)
        else:  # Re-predict entire log
            train = res[res[tgt].notna()][feature_set + [tgt]].copy()

        if train.empty:
            continue

        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]
        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)

        # Train models and find best
        trained_models = {}
        best_model, best_name, best_mae = None, None, float("inf")

        for name, model in boosters:
            try:
                model.fit(Xtr, ytr)
                mae = mean_absolute_error(yval, model.predict(Xval))
                print(f'  {name}: MAE = {mae:.3f}')
                trained_models[name] = model
                if mae < best_mae:
                    best_model, best_name, best_mae = model, name, mae
            except Exception as e:
                print(f'  {name}: Failed - {str(e)}')

        if best_model is None:
            continue

        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')

        # Generate predictions
        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
        best_preds = best_model.predict(X_full)
        best_preds_series = pd.Series(best_preds, index=res.index)

        # Apply prediction mode logic
        if prediction_mode == 1:
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(best_preds_series)
            missing_count = res[tgt].isna().sum()
            print(f'Imputed {missing_count}/{len(res)} missing values ({missing_count/len(res):.1%})')
        elif prediction_mode == 2:
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = res[tgt].fillna(best_preds_series)
            if 'test_data' in locals():
                X_test = test_data.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
                test_preds = best_model.predict(X_test)
                test_mae = mean_absolute_error(test_data[tgt], test_preds)
                print(f'Cross-validation MAE: {test_mae:.3f}')
        else:  # Re-predict entire log
            res[f'{tgt}_pred'] = best_preds
            res[f'{tgt}_imputed'] = best_preds_series
            print(f'Re-predicted entire log ({len(res)} samples)')

        # Calculate errors
        res[f'{tgt}_error'] = np.abs(res[tgt] - best_preds_series) / res[tgt] * 100

        # Store results
        model_results[tgt] = {
            'trained_models': trained_models,
            'best_model_name': best_name,
            'best_mae': best_mae,
            'best_predictions': best_preds_series,
            'original_data': res[tgt].copy(),
            'prediction_mode': prediction_mode
        }

    return res, model_results

# Run imputation
DEPTH_COL = 'MD'
print(f'\n{"="*60}')
print('RUNNING ML IMPUTATION')
print(f'{"="*60}')
print(f'Target: {selected_target_log}, Features: {selected_input_logs}, Mode: {prediction_mode_name}')

results, model_results = impute_logs(well_data, DEPTH_COL, selected_input_logs,
                                   [selected_target_log], prediction_mode, 25.0)

print(f'\n✓ Processing completed! Results shape: {results.shape}')
new_columns = [col for col in results.columns if any(suffix in col for suffix in ['_pred', '_imputed', '_error'])]
print(f'New columns: {new_columns}')

# Streamlined visualization function
def create_visualization(results_df, model_results, target_log, selected_wells):
    """Streamlined visualization combining all essential plots."""
    print(f'\n{"="*60}')
    print('GENERATING VISUALIZATION')
    print("="*60)

    if target_log not in model_results:
        print(f'No model results for {target_log}')
        return

    try:
        import matplotlib.pyplot as plt

        target_data = model_results[target_log]
        well_names = [w.petrel_name for w in selected_wells]
        original_data = target_data['original_data']
        best_preds = target_data['best_predictions']
        best_model_name = target_data['best_model_name']

        # Create comprehensive plot
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Plot 1: Multi-well log comparison
        for well_name in well_names[:5]:  # Limit to first 5 wells for clarity
            well_mask = results_df['WELL'] == well_name
            well_data = results_df[well_mask]
            if well_data.empty:
                continue

            well_md = well_data['MD']
            well_original = original_data[well_mask]
            well_preds = best_preds[well_mask]

            # Plot original data
            valid_original = ~well_original.isna()
            if valid_original.any():
                ax1.plot(well_original[valid_original], well_md[valid_original],
                        'o-', markersize=2, linewidth=1, alpha=0.7, label=f'{well_name} Original')

            # Plot predictions
            ax1.plot(well_preds, well_md, '--', linewidth=2, alpha=0.8,
                    label=f'{well_name} Predicted')

        ax1.set_ylabel('Measured Depth (MD)')
        ax1.set_xlabel(f'{target_log} Values')
        ax1.set_title(f'Multi-Well Comparison: {target_log}')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()

        # Plot 2: Cross-plot (all wells combined)
        valid_mask = ~original_data.isna()
        if valid_mask.any():
            original_valid = original_data[valid_mask]
            preds_valid = best_preds[valid_mask]

            ax2.scatter(original_valid, preds_valid, alpha=0.6, s=20)

            # Perfect prediction line
            min_val = min(original_valid.min(), preds_valid.min())
            max_val = max(original_valid.max(), preds_valid.max())
            ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect')

            # Calculate R²
            r2 = r2_score(original_valid, preds_valid)
            ax2.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax2.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        ax2.set_xlabel(f'Original {target_log}')
        ax2.set_ylabel(f'Predicted {target_log}')
        ax2.set_title(f'Prediction Quality ({best_model_name})')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Error distribution
        if valid_mask.any():
            errors = np.abs(original_valid - preds_valid)
            ax3.hist(errors, bins=30, alpha=0.7, color='lightblue', edgecolor='black')
            ax3.axvline(np.mean(errors), color='red', linestyle='--',
                       label=f'Mean: {np.mean(errors):.3f}')
            ax3.axvline(np.median(errors), color='orange', linestyle='--',
                       label=f'Median: {np.median(errors):.3f}')
            ax3.set_xlabel('Absolute Error')
            ax3.set_ylabel('Frequency')
            ax3.set_title('Error Distribution')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # Plot 4: Data coverage summary
        coverage_by_well = []
        for well_name in well_names:
            well_mask = results_df['WELL'] == well_name
            well_original = original_data[well_mask]
            coverage = (~well_original.isna()).sum() / len(well_original) * 100 if len(well_original) > 0 else 0
            coverage_by_well.append(coverage)

        bars = ax4.bar(range(len(well_names)), coverage_by_well, alpha=0.7, color='skyblue')
        ax4.set_xlabel('Wells')
        ax4.set_ylabel('Data Coverage (%)')
        ax4.set_title(f'{target_log} Coverage by Well')
        ax4.set_xticks(range(len(well_names)))
        ax4.set_xticklabels([w[:8] + '...' if len(w) > 8 else w for w in well_names], rotation=45)
        ax4.grid(True, alpha=0.3)

        # Add coverage values on bars
        for i, coverage in enumerate(coverage_by_well):
            ax4.text(i, coverage + 1, f'{coverage:.1f}%', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.suptitle(f'ML Imputation Results: {target_log} (Best Model: {best_model_name})',
                    fontsize=14, fontweight='bold', y=1.02)
        plt.show()

        # Print summary statistics
        print(f'\nSUMMARY STATISTICS:')
        print(f'Best Model: {best_model_name} (MAE: {target_data["best_mae"]:.3f})')
        if valid_mask.any():
            print(f'Overall R²: {r2_score(original_valid, preds_valid):.3f}')
        print(f'Total Wells: {len(well_names)}')
        print(f'Total Data Points: {len(original_data):,}')
        print(f'Available Data: {(~original_data.isna()).sum():,} ({(~original_data.isna()).sum()/len(original_data)*100:.1f}%)')
        print(f'Missing Data: {original_data.isna().sum():,} ({original_data.isna().sum()/len(original_data)*100:.1f}%)')

    except ImportError:
        print('⚠ Matplotlib not available. Showing text summary only.')
        print(f'Best Model: {target_data["best_model_name"]} (MAE: {target_data["best_mae"]:.3f})')
    except Exception as e:
        print(f'Error in visualization: {str(e)}')

# Generate visualization
create_visualization(results, model_results, selected_target_log, selected_wells)

# Streamlined write-back function
def write_back_to_petrel(results_df, log_name_in_results, clone_from, new_log_name=None):
    """Streamlined write-back function with proper Petrel collection handling."""
    if new_log_name is None:
        new_log_name = f'{selected_target_log}_ML_imputed'

    print(f'\nWriting {log_name_in_results} as {new_log_name} to {len(selected_wells)} wells...')

    # Helper function to find global well log by name (from working version)
    def find_global_well_log_by_name(name):
        """Helper function to find global well log by name"""
        for item in petrel.global_well_logs:
            if isinstance(item, list):
                for obj in item:
                    if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                        return obj
            elif hasattr(item, 'petrel_name') and item.petrel_name == name:
                return item
        return None

    # Helper to handle Petrel collections that may contain lists
    def get_object_list(collection):
        object_list = []
        for item in collection:
            if isinstance(item, list):
                object_list.extend(item)
            else:
                object_list.append(item)
        return object_list

    # Find template log
    log_to_clone = find_global_well_log_by_name(clone_from)
    if log_to_clone is None:
        print(f"ERROR: Template log {clone_from} not found")
        return

    success_count = 0
    for w in selected_wells:
        well_df = results_df[results_df['WELL'] == w.petrel_name]
        if well_df.empty:
            continue

        # Clean data
        md_values = well_df['MD'].values
        log_values = well_df[log_name_in_results].values
        valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)

        if not valid_mask.any():
            continue

        md_clean = md_values[valid_mask].tolist()
        values_clean = log_values[valid_mask].tolist()

        try:
            # Determine collection type and get objects
            if isinstance(log_to_clone, DiscreteGlobalWellLog):
                gwl_collection = petrel.discrete_global_well_logs
            else:
                gwl_collection = petrel.global_well_logs

            # Check if global log exists using proper collection handling
            gwl = [i for i in get_object_list(gwl_collection) if hasattr(i, 'petrel_name') and i.petrel_name == new_log_name]

            # Create global log if needed
            if not gwl:
                global_log = log_to_clone.clone(name_of_clone=new_log_name)
                gwl = [global_log]

            # Check well log using proper collection handling
            well_log = [i for i in get_object_list(w.logs) if hasattr(i, 'petrel_name') and i.petrel_name == new_log_name]

            if well_log:
                # Update existing
                well_log[0].readonly = False
                well_log[0].set_values(md_clean, values_clean)
            else:
                # Create new
                new_log = gwl[0].create_well_log(w)
                new_log.readonly = False
                new_log.set_values(md_clean, values_clean)

            success_count += 1
            print(f'  ✓ {w.petrel_name}')

        except Exception as e:
            print(f'  ✗ {w.petrel_name}: {str(e)}')

    print(f'\n✓ Write-back completed: {success_count}/{len(selected_wells)} wells updated')

# Interactive session management
write_back_choice = console_select([1, 2, 3],
    "Choose action:\n  1. Write results to Petrel\n  2. End session\n  3. Continue working",
    default=3, auto_mode=AUTO_MODE)

if write_back_choice == 1:
    print('\n' + '='*60)
    print('WRITING RESULTS TO PETREL')
    print('='*60)

    log_to_write = f'{selected_target_log}_imputed'
    new_log_name = f'{selected_target_log}_ML_imputed'

    try:
        write_back_to_petrel(results, log_to_write, selected_target_log, new_log_name)
        print('✓ Write-back completed successfully!')
    except Exception as e:
        print(f'✗ Error during write-back: {str(e)}')

elif write_back_choice == 2:
    print('\n✓ Session ending...')
    connection_choice = console_select([1, 2],
        "Connection management:\n  1. Close Petrel connection\n  2. Keep connection open",
        default=2, auto_mode=AUTO_MODE)

    if connection_choice == 1:
        try:
            petrel.close()
            print('✓ Petrel connection closed.')
        except Exception as e:
            print(f'⚠ Error closing connection: {str(e)}')
else:
    print('\n✓ Continuing session. Results available in "results" variable.')
    print('Call write_back_to_petrel() manually if needed later.')

# Final summary
print(f"\n{'='*80}")
print("WORKFLOW COMPLETED")
print('='*80)
print(f"✓ Project: {petrel.get_current_project_name()}")
print(f"✓ Wells processed: {len(selected_wells)}")
print(f"✓ Target log: {selected_target_log}")
print(f"✓ Prediction mode: {prediction_mode_name}")
print(f"✓ Results shape: {results.shape}")

if selected_target_log in model_results:
    best_model = model_results[selected_target_log]['best_model_name']
    best_mae = model_results[selected_target_log]['best_mae']
    print(f"✓ Best model: {best_model} (MAE: {best_mae:.3f})")

print(f"\nRESULTS AVAILABLE:")
print("- results: Main DataFrame with predictions")
print("- model_results: Model performance data")
print('='*80)
